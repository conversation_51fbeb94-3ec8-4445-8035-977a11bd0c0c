{"name": "beba<PERSON>-webapp", "version": "0.0.1", "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview"}, "devDependencies": {"@sveltejs/adapter-auto": "^3.0.0", "@sveltejs/kit": "^2.4.1", "@sveltejs/vite-plugin-svelte": "^3.0.0", "svelte": "^4.2.7", "svelte-icons": "^2.1.0", "vite": "^5.0.3"}, "type": "module", "dependencies": {"@dhaiwat10/react-link-preview": "^1.15.0", "@fortawesome/fontawesome-free": "^6.5.1", "@sveltejs/adapter-node": "^4.0.0", "svelte-infinite-loading": "^1.3.8", "svelte-routing": "^2.10.0", "sweetalert2": "^11.10.1"}}