<!DOCTYPE html>
<html lang="en">
  <head>
    <meta name="p:domain_verify" content="de39bf3c592d9fbd9d369f6517e76485" />
    <meta charset="utf-8" />
    <link rel="icon" href="%sveltekit.assets%/favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <meta property="og:site_name" content="Zupid" />
    <meta property="og:locale" content="en_IN" />
    <meta property="og:type" content="website" />
    <meta
      property="og:title"
      content="Zupid - Match by Vibe, Not Just Looks | India's Leading Dating App"
    />
    <meta
      property="og:description"
      content="Zupid - Match by Vibe, Not Just Looks. India's leading dating app for genuine connections, based on personality & shared interests. Secure, spam-free dating with verified profiles."
    />
    <meta property="og:url" content="https://www.zupid.net" />
    <meta
      property="og:image"
      content="https://www.zupid.net/zupid_1000_500.png"
    />
    <meta property="og:image:width" content="1000" />
    <meta property="og:image:height" content="500" />
    <meta
      property="og:image:alt"
      content="Zupid - India's Vibe-Based Dating App - Match by Vibe, Not Just Looks"
    />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@ZupidApp" />
    <meta name="twitter:creator" content="@ZupidApp" />
    <meta
      name="twitter:title"
      content="Zupid - Match by Vibe, Not Just Looks | India's Leading Dating App"
    />
    <meta
      name="twitter:description"
      content="Zupid - Match by Vibe, Not Just Looks. India's leading dating app for genuine connections, based on personality & shared interests. Secure, spam-free dating with verified profiles."
    />
    <meta
      name="twitter:image"
      content="https://www.zupid.net/zupid_1000_500.png"
    />
    <meta
      name="twitter:image:alt"
      content="Zupid - India's Vibe-Based Dating App - Match by Vibe, Not Just Looks"
    />

    <!-- ✅ Structured Data for Google Search -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "Zupid",
        "url": "https://www.zupid.net",
        "description": "Zupid - Match by Vibe, Not Just Looks. Discover India's leading dating app for genuine connections, based on personality & shared interests.",
        "publisher": {
          "@type": "Organization",
          "name": "Zupid",
          "logo": {
            "@type": "ImageObject",
            "url": "https://www.zupid.net/heart_zupid_full_logo_500.png",
            "width": 500,
            "height": 500
          }
        },
        "image": "https://www.zupid.net/heart_zupid_full_logo_500.png"
      }
    </script>

    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
    <link
      rel="preload"
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap"
      as="style"
    />

    %sveltekit.head%
    <script type="text/javascript">
      !(function () {
        "use strict";
        !(function (e, t) {
          var n = e.amplitude || { _q: [], _iq: {} };
          if (n.invoked)
            e.console &&
              console.error &&
              console.error("Amplitude snippet has been loaded.");
          else {
            var r = function (e, t) {
                e.prototype[t] = function () {
                  return (
                    this._q.push({
                      name: t,
                      args: Array.prototype.slice.call(arguments, 0),
                    }),
                    this
                  );
                };
              },
              s = function (e, t, n) {
                return function (r) {
                  e._q.push({
                    name: t,
                    args: Array.prototype.slice.call(n, 0),
                    resolve: r,
                  });
                };
              },
              o = function (e, t, n) {
                e[t] = function () {
                  if (n)
                    return {
                      promise: new Promise(
                        s(e, t, Array.prototype.slice.call(arguments))
                      ),
                    };
                };
              },
              i = function (e) {
                for (var t = 0; t < m.length; t++) o(e, m[t], !1);
                for (var n = 0; n < g.length; n++) o(e, g[n], !0);
              };
            n.invoked = !0;
            var u = t.createElement("script");
            (u.type = "text/javascript"),
              (u.integrity =
                "sha384-x0ik2D45ZDEEEpYpEuDpmj05fY91P7EOZkgdKmq4dKL/ZAVcufJ+nULFtGn0HIZE"),
              (u.crossOrigin = "anonymous"),
              (u.async = !0),
              (u.src =
                "https://cdn.amplitude.com/libs/analytics-browser-2.0.0-min.js.gz"),
              (u.onload = function () {
                e.amplitude.runQueuedFunctions ||
                  console.log("[Amplitude] Error: could not load SDK");
              });
            var a = t.getElementsByTagName("script")[0];
            a.parentNode.insertBefore(u, a);
            for (
              var c = function () {
                  return (this._q = []), this;
                },
                p = [
                  "add",
                  "append",
                  "clearAll",
                  "prepend",
                  "set",
                  "setOnce",
                  "unset",
                  "preInsert",
                  "postInsert",
                  "remove",
                  "getUserProperties",
                ],
                l = 0;
              l < p.length;
              l++
            )
              r(c, p[l]);
            n.Identify = c;
            for (
              var d = function () {
                  return (this._q = []), this;
                },
                f = [
                  "getEventProperties",
                  "setProductId",
                  "setQuantity",
                  "setPrice",
                  "setRevenue",
                  "setRevenueType",
                  "setEventProperties",
                ],
                v = 0;
              v < f.length;
              v++
            )
              r(d, f[v]);
            n.Revenue = d;
            var m = [
                "getDeviceId",
                "setDeviceId",
                "getSessionId",
                "setSessionId",
                "getUserId",
                "setUserId",
                "setOptOut",
                "setTransport",
                "reset",
                "extendSession",
              ],
              g = [
                "init",
                "add",
                "remove",
                "track",
                "logEvent",
                "identify",
                "groupIdentify",
                "setGroup",
                "revenue",
                "flush",
              ];
            i(n),
              (n.createInstance = function (e) {
                return (n._iq[e] = { _q: [] }), i(n._iq[e]), n._iq[e];
              }),
              (e.amplitude = n);
          }
        })(window, document);
      })();

      amplitude.init("eb58cac9a8c681cd9fdb1d13703a3c6d", null, {
        saveEvents: true,
        includeUtm: true,
        includeReferrer: true,
      });
      /////
      // amplitude.getInstance().logEvent("Main Page");
      //
    </script>
    <script
      async
      src="https://www.googletagmanager.com/gtag/js?id=G-SXZPM2NWEQ"
    ></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag("js", new Date());

      gtag("config", "G-SXZPM2NWEQ");
    </script>
  </head>
  <body data-sveltekit-preload-data="hover">
    <div style="display: contents">%sveltekit.body%</div>
  </body>
</html>

<style>
  html,
  body {
    background-color: #000000;
    max-width: 100%;
    overflow-x: hidden;
  }
  * {
    margin: 0;
    padding: 0;
    font-family: "Poppins", sans-serif;
  }
</style>
