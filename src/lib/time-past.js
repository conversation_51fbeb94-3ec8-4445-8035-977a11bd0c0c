export function formatTimeDifference(updatedAt) {
  const currentTime = new Date();
  const updatedTime = new Date(updatedAt);
  const timeDifference = currentTime - updatedTime;

  const minutes = Math.floor(timeDifference / (1000 * 60));
  const hours = Math.floor(timeDifference / (1000 * 60 * 60));
  const days = Math.floor(timeDifference / (1000 * 60 * 60 * 24));

  let timeStr;
  if (minutes < 60) {
    timeStr = `${minutes} minute${minutes !== 1 ? "s" : ""} ago`;
  } else if (hours < 24) {
    timeStr = `${hours} hour${hours !== 1 ? "s" : ""} ago`;
  } else {
    timeStr = `${days} day${days !== 1 ? "s" : ""} ago`;
  }
  return{
    timeStr
  }
}
