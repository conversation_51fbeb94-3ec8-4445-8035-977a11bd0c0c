<script>
  import { WEBSITE_ADDRESS } from "$lib/common-constant";
  export let post;
  
  let blog_images_folder = 'https://pub-d4e5dd63bd1b4795a9099259d8cb2e8a.r2.dev/zupid%2F';
  
  const MAX_WORDS = 60;
  function truncateText(text) {
    const words = text.split(' ');
    if (words.length > MAX_WORDS) {
      return words.slice(0, MAX_WORDS).join(' ') + '...';
    }
    return text;
  }
</script>

<a rel="external" href="{WEBSITE_ADDRESS}/blog/{post.post_name}" class="post-link">
    <article class="post">
        <div class="post-image-container">
            <img src="{blog_images_folder}{post.photo}" alt="{post.title}" class="post-image" />
        </div>
        <div class="post-content">
            <h2 class="post-title">{post.title}</h2>
            <p class="post-excerpt">{truncateText(post.body)}</p>
            <span class="read-more">
                Read more <i class="fas fa-arrow-right"></i>
            </span>
        </div>
    </article>
</a>

<style>
    .post-link {
        display: block;
        height: 100%;
        text-decoration: none;
        color: inherit;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .post-link:hover {
        transform: translateY(-4px);
    }
    
    .post {
        height: 100%;
        display: flex;
        flex-direction: column;
        background-color: #121212;
        border-radius: 12px;
        overflow: hidden;
        border: 1px solid #252525;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .post-link:hover .post {
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
        border-color: #353535;
    }
    
    .post-image-container {
        width: 100%;
        height: 220px;
        overflow: hidden;
    }
    
    .post-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
    }

    .post-link:hover .post-image {
        transform: scale(1.05);
    }
    
    .post-content {
        padding: 1.5rem;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
    }
    
    .post-title {
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 0.75rem;
        color: #ffaa50;
        line-height: 1.4;
    }
    
    .post-excerpt {
        color: #b0b0b0;
        font-size: 0.95rem;
        line-height: 1.6;
        margin-bottom: 1rem;
        flex-grow: 1;
    }
    
    .read-more {
        color: #cb6ce6;
        font-weight: 500;
        font-size: 0.9rem;
        display: inline-flex;
        align-items: center;
        margin-top: auto;
        transition: color 0.3s ease;
    }

    .post-link:hover .read-more {
        color: #e090ff;
    }

    .read-more i {
        margin-left: 6px;
        transition: transform 0.3s ease;
    }

    .post-link:hover .read-more i {
        transform: translateX(3px);
    }
</style>