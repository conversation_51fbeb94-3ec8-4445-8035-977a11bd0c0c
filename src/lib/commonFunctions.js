import { API_ADDRESS } from "$lib/common-constant";
import Swal from "sweetalert2";


export async function getPosts(fetch, params) {
  let id = params.id;
  const response = await fetch(`${API_ADDRESS}/blog/${id}`);
  return response.json();
}

export async function getRandomBlogPosts(fetch) {
  const response = await fetch(`${API_ADDRESS}/randomblog`);
  return response.json();
}

export async function getHomePagePosts(fetch, offset) {
  const response = await fetch(`${API_ADDRESS}/blog`, {
    method: "POST",
    headers: {
      "Content-Type": "text/plain",
    },
    body: JSON.stringify({ offset }),
  });

  return response.json();
}

export async function getResourcesPosts(fetch) {
  const response = await fetch(`${API_ADDRESS}/resources`);
  return response.json();
}
//
//

export function handleShareClick(linkToShare) {
  copyToClipboard(linkToShare);
  // Use SweetAlert2 for a beautiful alert
  Swal.fire({
    position: "top",
    icon: "success",
    title: "Link Copied",
    showConfirmButton: false,
    timer: 500,
  });
}

function copyToClipboard(text) {
  const textArea = document.createElement("textarea");
  textArea.value = text;
  document.body.appendChild(textArea);
  textArea.select();
  document.execCommand("copy");
  document.body.removeChild(textArea);
}
