<script>
  export let isMainPage;
  let color = "#cb6ce6";
  if (isMainPage) {
    color = "#cb6ce6";
  }
</script>

<footer class="footer">
  <!-- Top accent line -->
  <div class="footer-accent" style={`background-color: ${color}`}></div>
  
  <!-- Links (grid layout for better responsiveness) -->
  <div class="footer-links">
    <a href="https://play.google.com/store/apps/details?id=net.zupid.app&utm_source=website&utm_medium=footer_button" class="footer-link" on:click={() => amplitudeClickFunction('Click_Footer_DownloadApp')}>Download App</a>
    
    <a href="/campus-ambassador" class="footer-link" on:click={() => amplitudeClickFunction('Click_Footer_CampusAmbassador')}>Campus Ambassador</a>
    <a href="/insights" class="footer-link" on:click={() => amplitudeClickFunction('Click_Footer_Insights')}>Insights</a>
    <a href="/blog" class="footer-link" on:click={() => amplitudeClickFunction('Click_Footer_Blog')}>Blog</a>
    
    <a href="/#about" class="footer-link" on:click={() => amplitudeClickFunction('Click_Footer_AboutUs')}>About Us</a>
    
    <a href="/privacy" class="footer-link" on:click={() => amplitudeClickFunction('Click_Footer_PrivacyPolicy')}>Privacy Policy</a>
    <a href="/terms-and-conditions" class="footer-link" on:click={() => amplitudeClickFunction('Click_Footer_Terms&Conditions')}>Terms</a>
    <a href="/cancel-and-refund" class="footer-link" on:click={() => amplitudeClickFunction('Click_Footer_Cancel&Refund')}>Refunds</a>
    <a href="/resources" class="footer-link" on:click={() => amplitudeClickFunction('Click_Footer_Resources')}>Resources</a>
    
  </div>

  <!-- Logo (centered) -->
  <div class="footer-logo">
    <!-- {#if isMainPage} -->
      <a href="https://www.zupid.net" rel="external">
        <img src="/transparent_lower_zupid_400_200.webp" alt="Zupid Dating App" class="logo-main">
      </a>
    <!-- {:else} -->
     <!-- {/if} -->
  </div>

  <!-- Social media icons (flex layout) -->
  <div class="social-icons">
    <a href="https://www.youtube.com/@zupid_dating" target="_blank" aria-label="YouTube">
      <img src="/social-media/youtube.webp" alt="YouTube" class="social-icon">
    </a>
    <a href="https://www.instagram.com/zupid_dating" target="_blank" aria-label="Instagram">
      <img src="/social-media/instagram.webp" alt="Instagram" class="social-icon">
    </a>
    <a href="https://twitter.com/zupid_dating" target="_blank" aria-label="Twitter">
      <img src="/social-media/twitter.webp" alt="Twitter" class="social-icon">
    </a>
  </div>

  <!-- Copyright text -->
  <p class="copyright">© {new Date().getFullYear()} Zippybits Technologies Private Limited. All Rights Reserved.</p>
</footer>

<style>
  .footer {
    width: 100%;
    background-color: transparent;
    padding: 40px 0 30px;
    text-align: center;
    font-family: 'Inter', -apple-system, sans-serif;
  }

  .footer-accent {
    height: 2px;
    width: 80px;
    margin: 0 auto 30px;
    background: #cb6ce6;
  }

  .footer-links {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 16px;
    margin-bottom: 30px;
  }

  .footer-link {
    color: #cb6ce6;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: opacity 0.3s ease;
  }

  .footer-link:hover {
    opacity: 0.8;
  }

  .footer-logo {
    margin: 25px 0;
  }

  .footer-logo img {
    height: 50px;
    transition: transform 0.3s ease;
  }

  .footer-logo img:hover {
    transform: scale(1.05);
  }

  .social-icons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin: 25px 0;
  }

  .social-icon {
    width: 24px;
    height: 24px;
    opacity: 0.8;
    transition: opacity 0.3s ease;
  }

  .social-icon:hover {
    opacity: 1;
  }

  .copyright {
    color: rgba(203, 108, 230, 0.7);
    font-size: 13px;
    margin-top: 20px;
  }

  /* Mobile responsiveness */
  @media (max-width: 640px) {
    .footer-links {
      gap: 12px 16px;
    }
    .footer-link {
      font-size: 13px;
    }
  }
</style>