<script>
  import { onMount } from 'svelte';
  
  // Form state
  let showForm = false;
  let name = '';
  let email = '';
  let phone = '';
  let isSubmitting = false;
  let submitSuccess = false;
  let error = '';
  
  // Animation state
  let mounted = false;
  onMount(() => { mounted = true; });
  const playstoreLink = "https://play.google.com/store/apps/details?id=net.zupid.app&utm_source=website&utm_medium=download_button";
  
  async function submitForm() {
    // Clear previous errors
    error = '';
    
    // Validate required fields
    if (!email || !phone) {
      error = 'Please fill in required fields';
      return;
    }
    
    isSubmitting = true;
    
    try {
      const formData = new FormData();
      formData.append('_replyto', email);
      formData.append('name', name.trim());
      formData.append('email', email.trim());
      formData.append('phone', phone.trim());
      formData.append('_subject', 'New Waitlist Signup');
      formData.append('_template', 'table');
      formData.append('_captcha', 'false'); // Disable captcha for better UX
      
      const response = await fetch('https://formsubmit.co/ajax/<EMAIL>', {
        method: 'POST',
        body: formData
      });
      
      const result = await response.json();
      
      if (response.ok && result.success === 'true') {
        submitSuccess = true;
        resetForm();
        setTimeout(() => {
          showForm = false;
          submitSuccess = false;
        }, 2000);
      } else {
        throw new Error(result.message || 'Submission failed');
      }
      
    } catch (err) {
      error = 'Failed to submit. Please try again later.';
      console.error('Submission error:', err);
    } finally {
      isSubmitting = false;
    }
  }
  
  function resetForm() {
    name = '';
    email = '';
    phone = '';
  }
  
  function closeForm() {
    showForm = false;
    submitSuccess = false;
    error = '';
  }
</script>

<style>
  /* Banner Styles */
  .bottom-banner {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background: rgba(36, 31, 31, 0.96);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(203, 108, 230, 0.15);
    display: flex;
    justify-content: center;
    padding: 16px;
    z-index: 100;
    transform: translateY(100%);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  }
  
  .bottom-banner.visible {
    transform: translateY(0);
    opacity: 1;
  }
  
  .cta-button {
    background: linear-gradient(135deg, #cb6ce6 0%, #db6de6 100%);
    border: none;
    color: #000;
    padding: 16px 32px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(203, 108, 230, 0.3);
    display: flex;
    align-items: center;
    gap: 10px;
    outline: none;
    text-decoration: none;
  }
  
  .cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(203, 108, 230, 0.4);
  }
  
  /* Form Modal Styles */
  .form-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.85);
    backdrop-filter: blur(8px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 20px;
    animation: fadeIn 0.3s ease-out;
  }
  
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  .form-container {
    background: #1a1a1a;
    border-radius: 16px;
    padding: 30px;
    width: 100%;
    max-width: 450px;
    border: 1px solid rgba(203, 108, 230, 0.3);
    box-shadow: 0 10px 30px rgba(203, 108, 230, 0.2);
    position: relative;
    animation: slideUp 0.3s ease-out;
  }
  
  @keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }
  
  .close-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    background: none;
    border: none;
    color: #cb6ce6;
    font-size: 24px;
    cursor: pointer;
    transition: transform 0.2s ease;
    padding: 5px;
    outline: none;
  }
  
  .close-btn:hover {
    transform: rotate(90deg);
  }
  
  .form-title {
    color: #cb6ce6;
    text-align: center;
    margin-bottom: 25px;
    font-size: 1.5rem;
    font-weight: 600;
  }
  
  .form-group {
    margin-bottom: 20px;
  }
  
  .form-label {
    display: block;
    color: rgba(255,255,255,0.8);
    margin-bottom: 8px;
    font-size: 14px;
  }
  
  .form-input {
    width: 100%;
    background: rgba(255,255,255,0.05);
    border: 1px solid rgba(255,255,255,0.1);
    border-radius: 8px;
    padding: 12px 15px;
    color: white;
    font-size: 16px;
    transition: all 0.3s ease;
    outline: none;
  }
  
  .form-input:focus {
    border-color: #cb6ce6;
    box-shadow: 0 0 0 2px rgba(203, 108, 230, 0.2);
  }
  
  .submit-btn {
    width: 100%;
    background: linear-gradient(135deg, #cb6ce6 0%, #db6de6 100%);
    color: #000;
    border: none;
    padding: 14px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
    cursor: pointer;
    margin-top: 10px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    outline: none;
  }
  
  .submit-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(203, 108, 230, 0.4);
  }
  
  .submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none !important;
  }
  
  .error-message {
    color: #ff6b6b;
    font-size: 14px;
    margin-top: 5px;
    text-align: center;
  }
  
  .success-message {
    color: white;
    text-align: center;
    padding: 40px 20px;
  }
  
  .success-icon {
    font-size: 50px;
    margin-bottom: 20px;
    color: #cb6ce6;
    animation: bounce 0.6s;
  }
  
  @keyframes bounce {
    0%, 20%, 50%, 80%, 100% {transform: translateY(0);}
    40% {transform: translateY(-15px);}
    60% {transform: translateY(-7px);}
  }
  
  @media (max-width: 768px) {
    .form-container {
      padding: 25px 20px;
    }
    
    .form-title {
      font-size: 1.3rem;
      margin-bottom: 20px;
    }
    
    .cta-button {
      padding: 14px 24px;
      font-size: 15px;
      text-decoration: none;
    }
  }
  /* Add these mobile-specific styles to your existing styles */
  @media (max-width: 768px) {
    .form-overlay {
      padding: 10px;
      box-sizing: border-box;
      overflow-y: auto; /* Allows scrolling if needed */
    }
    
    .form-container {
      max-width: 100%;
      margin: 10px;
      box-sizing: border-box;
      max-height: 90vh; /* Prevents form from exceeding screen height */
      overflow-y: auto; /* Makes form scrollable if content is too long */
    }
    
    /* Keep all your existing mobile styles below */
    .form-title {
      font-size: 1.3rem;
      margin-bottom: 20px;
    }
    
    .cta-button {
      padding: 14px 24px;
      font-size: 15px;
      text-decoration: none;
    }
  }
</style>

<div class="bottom-banner" class:visible={mounted}>
  <a class="cta-button" href={playstoreLink} target="_blank" rel="noopener">
    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" style="margin-right: 8px;">
      <path d="M17 2H7C5.89543 2 5 2.89543 5 4V20C5 21.1046 5.89543 22 7 22H17C18.1046 22 19 21.1046 19 20V4C19 2.89543 18.1046 2 17 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M12 18H12.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
    DOWNLOAD ZUPID APP
  </a>
</div>


<!-- Banner Button -->
<!-- <div class="bottom-banner" class:visible={mounted}>
  <button class="cta-button" on:click={() => showForm = true}>
    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" style="margin-right: 8px;">
      <path d="M17 2H7C5.89543 2 5 2.89543 5 4V20C5 21.1046 5.89543 22 7 22H17C18.1046 22 19 21.1046 19 20V4C19 2.89543 18.1046 2 17 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M12 18H12.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
    DOWNLOAD APP
  </button>
</div> -->
<!-- Form Modal -->
<!-- {#if showForm}
  <div class="form-overlay">
    <div class="form-container">
      <button class="close-btn" on:click={closeForm} aria-label="Close form">×</button>
      
      {#if !submitSuccess}
        <h3 class="form-title">Get Early Access</h3>
        
        <form on:submit|preventDefault={submitForm}>
          <div class="form-group">
            <label for="name" class="form-label">Name (Optional)</label>
            <input id="name" class="form-input" type="text" bind:value={name} placeholder="Your name">
          </div>
          
          <div class="form-group">
            <label for="email" class="form-label">Email *</label>
            <input id="email" class="form-input" type="email" bind:value={email} required placeholder="<EMAIL>">
          </div>
          
          <div class="form-group">
            <label for="phone" class="form-label">Phone Number *</label>
            <input id="phone" class="form-input" type="tel" bind:value={phone} required placeholder="1234567890">
          </div>
          
          {#if error}
            <div class="error-message">{error}</div>
          {/if}
          
          <button class="submit-btn" type="submit" disabled={isSubmitting}>
            {#if isSubmitting}
              <span class="loading-indicator">Submitting...</span>
            {:else}
              Join Waitlist
            {/if}
          </button>
        </form>
      {:else}
        <div class="success-message">
          <div class="success-icon">🎉</div>
          <h3>You're on the list!</h3>
          <p>We'll notify you when we launch.</p>
        </div>
      {/if}
    </div>
  </div>
{/if} -->