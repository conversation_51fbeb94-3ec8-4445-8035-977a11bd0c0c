import { API_ADDRESS } from "$lib/common-constant";
let posts = []; // list of posts containing a slug [{title: "Test title", slug: "test-title", updatedAt: "2023-01-01"}]
let pages = [
  "blog",
  "cancel-and-refund",
  "terms-and-conditions",
  "privacy",
  "resources",
  "insights",
  "insights/statewise",
  "insights/cities",
  "insights/movies",
  "contact-us",
  "campus-ambassador",
]; // list of pages as a string ex. ["about", "blog", "contact"]
const site = "https://www.zupid.net";
// Get current date
const today = new Date();

function formatDateToISO(date) {
  return date.toISOString().split("T")[0];
}

// /** @type {import('./$types').RequestHandler} */
export async function GET({ url }) {
  // Fetch posts from the API
  const postsResponse = await fetch(`${API_ADDRESS}/postsitemap`);

  if (!postsResponse.ok) {
    // Handle error if the fetch fails
    return new Response("Error fetching posts", { status: 500 });
  }

  // Parse the JSON response
  posts = await postsResponse.json();
  console.log(posts);

  // Create the sitemap
  const body = sitemap(posts, pages);

  // Create the response
  const response = new Response(body);
  response.headers.set("Cache-Control", "max-age=0, s-maxage=3600");
  response.headers.set("Content-Type", "application/xml");

  return response;
}

const sitemap = (posts, pages) => `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${site}</loc>
    <priority>1.00</priority>
  </url>
  ${pages
    .map(
      (page) => `
  <url>
    <loc>${site}/${page}</loc>
    <priority>0.80</priority>
  </url>
  `
    )
    .join("")}
  ${posts
    .map((post) =>
      post.visible
        ? null
        : `
  <url>
    <loc>${site}/blog/${post.post_name}</loc>
    <priority>0.70</priority>
  </url>
  `
    )
    .join("")}
</urlset>`;
