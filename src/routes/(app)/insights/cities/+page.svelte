<style>
  :global(body) {
  margin: 0;
  padding: 0;
  font-family: 'Poppins', sans-serif;
  color: #ddd;
  background: linear-gradient(135deg, #1e1a29 0%, #2a2340 100%);
  min-height: 100vh;
}

.page-wrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.coming-soon-wrapper {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.coming-soon-container {
  background: rgba(38, 30, 59, 0.5);
  backdrop-filter: blur(18px);
  border-radius: 28px;
  padding: 3rem 4rem;
  max-width: 420px;
  text-align: center;
  box-shadow: 0 10px 40px rgba(107, 44, 134, 0.5);
}

h1 {
  font-size: 3rem;
  font-weight: 900;
  background: linear-gradient(90deg, #b76ee0, #6a1b9a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 1rem;
}

p {
  font-size: 1.25rem;
  color: #a98bd1;
  margin-bottom: 2rem;
}

.loader {
  border: 4px solid rgba(255, 255, 255, 0.15);
  border-top: 4px solid #b76ee0;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  margin: 0 auto;
  animation: spin 1.4s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg);}
  100% { transform: rotate(360deg);}
}

</style>


<script>
  import CuriousBanner from '$lib/curious-banner.svelte';
  import InsightsBackButton from '$lib/insights-back-button.svelte';
  import Footer from '$lib/footer.svelte';
</script>

<div class="page-wrapper">
  <main class="coming-soon-wrapper">
    <div class="coming-soon-container" role="alert" aria-live="polite">
      <InsightsBackButton />
      <h1>Coming Soon</h1>
      <p>Exciting new features are on their way! Stay tuned.</p>
      <div class="loader" aria-hidden="true"></div>
      <CuriousBanner />
    </div>
  </main>
  <Footer isMainPage={false} />
</div>

