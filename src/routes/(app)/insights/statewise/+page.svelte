<script>
  import data from '$lib/data/data.json';
  import BottomBanner from '$lib/bottom-banner.svelte';
  import Footer from '$lib/footer.svelte';
  import InsightsBackButton from '$lib/insights-back-button.svelte';
  
  

  const stateInterestMap = data.statewiseInterest.reduce((acc, item) => {
    if (!acc[item.state_name]) acc[item.state_name] = [];
    acc[item.state_name].push(item);
    return acc;
  }, {});

  const states = Object.entries(stateInterestMap)
    .map(([state, interests]) => {
      const totalUsers = interests.reduce((sum, i) => sum + i.user_count, 0);
      return { state, interests, totalUsers };
    })
    .sort((a, b) => b.totalUsers - a.totalUsers);

  let search = '';

  $: filteredStates = states.filter(({ state }) =>
    state.toLowerCase().includes(search.toLowerCase())
  );
</script>

<svelte:head>
  <title>Statewise Interests on Zupid - Discover Dating Trends by State</title>
  <meta name="description" content="Explore dating interests and trends statewise across India on Zupid. Find what people love in each state and connect through shared interests." />
  <meta name="keywords" content="dating trends India, statewise interests, dating interests by state, Zupid dating, Indian dating app, statewise dating data, dating insights India" />

  <meta name="robots" content="index, follow" />
  <link rel="canonical" href="https://www.zupid.net/insights/statewise" />

  <!-- Open Graph -->
  <meta property="og:title" content="Statewise Interests on Zupid - Discover Dating Trends by State" />
  <meta property="og:description" content="Explore dating interests and trends statewise across India on Zupid. Find what people love in each state and connect through shared interests." />
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://www.zupid.net/insights/statewise" />
  <meta property="og:image" content="https://www.zupid.net/heart_zupid_full_logo_500.png" />
</svelte:head>

<style>
  :global(body) {
    margin: 0;
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #0d0d0d, #1a0f23);
    color: #e6e6e6;
    background-attachment: fixed;
  }

  .container {
    max-width: 960px;
    margin: 4rem auto;
    padding: 2rem 1.5rem;
    background: rgba(255, 255, 255, 0.04);
    border-radius: 28px;
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  h1 {
    text-align: center;
    font-weight: 700;
    font-size: 2.5rem;
    margin-bottom: 2rem;
    background: linear-gradient(90deg, #9f80ff, #c56bfa);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  input[type="search"] {
    width: 100%;
    max-width: 420px;
    margin: 0 auto 2.5rem auto;
    display: block;
    padding: 0.9rem 1.3rem;
    border: none;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 500;
    color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 0 2px #9f80ff inset;
    backdrop-filter: blur(8px);
    transition: all 0.3s ease;
  }
  input[type="search"]:focus {
    outline: none;
    box-shadow: 0 0 12px rgba(180, 110, 255, 0.4), 0 0 0 3px #c56bfa inset;
    background: rgba(255, 255, 255, 0.15);
  }

  .state-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 18px;
    padding: 1.7rem 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  .state-card:hover {
    transform: translateY(-6px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.7);
  }

  .state-title {
    font-weight: 800;
    font-size: 1.7rem;
    color: #dbb7ff;
    margin-bottom: 1.2rem;
    text-transform: uppercase;
    letter-spacing: 0.06em;
  }

  ul.interests {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
  }

  li.interest-item {
    background: rgba(255, 255, 255, 0.07);
    border-radius: 14px;
    padding: 1rem 1.2rem;
    display: flex;
    align-items: center;
    width: 180px;
    box-shadow: 0 2px 6px rgba(160, 130, 250, 0.1);
    transition: box-shadow 0.3s ease, transform 0.2s ease;
    border: 1px solid rgba(255, 255, 255, 0.05);
  }
  li.interest-item:hover {
    box-shadow: 0 6px 18px rgba(160, 130, 250, 0.25);
    transform: translateY(-2px);
  }

  img.icon {
    width: 42px;
    height: 42px;
    border-radius: 12px;
    object-fit: cover;
    border: 2px solid #bda5ff;
    margin-right: 1rem;
    flex-shrink: 0;
  }

  .interest-text {
    flex-grow: 1;
  }
  .interest-name {
    font-weight: 700;
    font-size: 1.1rem;
    color: #d8c1ff;
    margin-bottom: 0.15rem;
  }
  .interest-category {
    font-size: 0.85rem;
    font-weight: 600;
    color: #b79ced;
    opacity: 0.8;
  }

  @media (max-width: 720px) {
    .state-card {
      padding: 1.2rem 1.4rem;
    }
    ul.interests {
      justify-content: center;
    }
    li.interest-item {
      width: 140px;
      flex-direction: column;
      align-items: center;
      text-align: center;
      gap: 0.4rem;
    }
    img.icon {
      margin-right: 0;
      margin-bottom: 0.3rem;
    }
  }

  .page-wrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

main.container {
  flex: 1;
}


</style>


<div class="page-wrapper">
  <main class="container" role="main" aria-label="Statewise Interests on Zupid">
    <InsightsBackButton />
    <h1>Top Interests by State on Zupid</h1>

    <input
      type="search"
      placeholder="Search states..."
      bind:value={search}
      aria-label="Search states"
    />

    {#if filteredStates.length === 0}
      <p style="text-align:center; font-weight:600; color:#999;">
        No states match your search.
      </p>
    {/if}

    {#each filteredStates as { state, interests }}
      <section class="state-card" role="region" aria-label={`Interests in ${state}`}>
        <h2 class="state-title">{state}</h2>
        <ul class="interests">
          {#each interests as { category, interest_name, user_percentage, image }}
            <li class="interest-item" tabindex="0">
              <img
                class="icon"
                src={`https://cdnpublic.zippybits.in/r2_zupid/interests/${image}`}
                alt={interest_name}
                loading="lazy"
              />
              <div class="interest-text">
                <div class="interest-name">{interest_name}</div>
                <div class="interest-category">{category}</div>
              </div>
            </li>
          {/each}
        </ul>
      </section>
    {/each}

    <div style="margin-top: 3rem; text-align: center; font-size: 0.95rem; color: #bfa7ff;">
      Curious why we track interests?
      <br />
      <a href="https://play.google.com/store/apps/details?id=net.zupid.app" target="_blank" rel="noopener"
         style="display: inline-block; margin-top: 0.8rem; padding: 10px 20px; background: rgba(255,255,255,0.06); border-radius: 12px; color: #fff; text-decoration: none; font-weight: 600; border: 1px solid rgba(255,255,255,0.12); transition: all 0.3s ease;">
        Explore the Zupid Dating App →
      </a>
    </div>
  </main>

  <Footer isMainPage={true} />
</div>
