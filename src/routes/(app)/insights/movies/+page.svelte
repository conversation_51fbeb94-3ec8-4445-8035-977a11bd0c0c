<script>
  // import movies from '$lib/data/movies.json'; // contains movie data
  import data from '$lib/data/data.json'; // contains movie data
  import Footer from '$lib/footer.svelte';
  import CuriousBanner from '$lib/curious-banner.svelte';
  import InsightsBackButton from '$lib/insights-back-button.svelte';

  let search = '';
  let movies = data.topMovies;
  $: filteredMovies = movies.filter(movie =>
    movie.title.toLowerCase().includes(search.toLowerCase())
  );
</script>

<svelte:head>
  <title>Top Movies Loved by Zupid Users - Dating Interests</title>
  <meta name="description" content="See what movies Zupid users love the most. Explore Bollywood and Hollywood titles that spark romantic vibes and conversations." />
  <meta name="keywords" content="top movies, Zupid users, dating movies, Bollywood, Hollywood, romantic films, Gen Z movies India" />
  <meta property="og:image" content="https://www.zupid.net/heart_zupid_full_logo_500.png" />
  <link rel="canonical" href="https://www.zupid.net/insights/movies" />
</svelte:head>

<style>
  :global(body) {
    background: linear-gradient(135deg, #0e0b16, #1a0f23);
    color: #eee;
  }

  .container {
    max-width: 960px;
    margin: 4rem auto;
    padding: 2rem 1.5rem;
    border-radius: 28px;
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(20px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
  }

  h1 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 800;
    background: linear-gradient(90deg, #ff99cc, #cc66ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 3rem;
  }

  input[type="search"] {
    width: 100%;
    max-width: 420px;
    margin: 0 auto 2rem auto;
    display: block;
    padding: 0.9rem 1.3rem;
    border-radius: 50px;
    font-size: 1rem;
    background: rgba(255,255,255,0.08);
    border: 1px solid rgba(255,255,255,0.1);
    color: #fff;
  }

  .movie-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1.5rem;
  }

  .movie-card {
    background: rgba(255,255,255,0.04);
    border-radius: 18px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    box-shadow: 0 10px 30px rgba(0,0,0,0.4);
  }

  .movie-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 16px 40px rgba(0,0,0,0.6);
  }

  .poster {
    width: 100%;
    height: 200px;
    object-fit: cover;
  }

  .movie-details {
    padding: 1rem;
  }

  .movie-title {
    font-size: 1.2rem;
    font-weight: 700;
    color: #eacaff;
  }

  .movie-meta {
    font-size: 0.9rem;
    color: #c1a4ff;
    margin-top: 0.4rem;
  }

  @media (max-width:480px) {
    .poster {
      height: 240px;
       }
    .movie-title {
      font-size: 1rem;
    }
  }

  .page-wrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

main.container {
  flex: 1;
}

</style>

<div class="page-wrapper">
  
  <main class="container">
    <InsightsBackButton />

    <h1>Top Movies & Shows Loved by Zupid Users</h1>
    
    <!-- <input type="search" bind:value={search} placeholder="Search movie name..." />

    {#if filteredMovies.length === 0}
      <p style="text-align: center; color: #aaa;">No results found.</p>
    {/if} -->

    <div class="movie-grid">
      {#each filteredMovies as movie}
        <div class="movie-card" tabindex="0">
          <img
            class="poster"
            src={`https://cdnpublic.zippybits.in/r2_zupid/movies/${movie.image_path}`}
            alt={`Poster of ${movie.title}`}
            loading="lazy"
          />

          <div class="movie-details">
            <div class="movie-title">{movie.title}</div>
            <div class="movie-meta">{movie.year}</div>
          </div>
        </div>
      {/each}
    </div>

    <CuriousBanner />
  </main>

  <Footer isMainPage={false} />
</div>
