
<svelte:head>
  <title>Zupid Insights - Explore Dating Trends and Interests</title>
  <meta name="description" content="Discover deep insights about dating interests across India including statewise trends, top cities, and more on Zupid." />
  <meta name="keywords" content="dating insights, dating trends India, statewise dating data, dating analytics, Zupid dating app, top cities dating trends" />
  <meta name="robots" content="index, follow" />
  
  <meta name="robots" content="index, follow" />
  <link rel="canonical" href="https://www.zupid.net/insights" />
  <!-- Open Graph -->
  <meta property="og:title" content="Zupid Insights - Explore Dating Trends and Interests" />
  <meta property="og:description" content="Discover deep insights about dating interests across India including statewise trends, top cities, and more on Zupid." />
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://www.zupid.net/insights" />
  <meta property="og:image" content="https://www.zupid.net/heart_zupid_full_logo_500.png" />
</svelte:head>

<script>
  import BottomBanner from '$lib/bottom-banner.svelte';
  import Footer from '$lib/footer.svelte';
  import CuriousBanner from '$lib/curious-banner.svelte';

  
    
  
  const insightSections = [
    {
    title: "Top Movies & Shows",
    description: "See which shows and movies spark the most conversations.",
    route: "/insights/movies",
    icon: "🎬"
    },
    {
      title: "Statewise Interests",
      description: "Explore interests across states and cities.",
      route: "/insights/statewise",
      icon: "📍"
    },
    {
      title: "Top Cities & Trends",
      description: "Discover where dating heats up the most.",
      route: "/insights/cities",
      icon: "🏙️"
    },
    // Uncomment later if needed:
    // {
    //   title: "User Demographics",
    //   description: "Insights by age, gender, and preferences.",
    //   route: "/insights/demographics",
    //   icon: "👥"
    // },
    // {
    //   title: "Interest Connections",
    //   description: "See how interests connect users across regions.",
    //   route: "/insights/connections",
    //   icon: "🔗"
    // },
  ];
</script>

<style>
  :global(html) {
  scroll-behavior: smooth;
}

:global(body) {
  background: linear-gradient(135deg, #1e1a29 0%, #2a2340 100%);
  font-family: 'Poppins', sans-serif;
  margin: 0;
  padding: 0;
  color: #ddd;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* better momentum scroll for iOS */
}

.container {
  min-height: 100vh;
  max-width: 960px;
  margin: 0 auto;
  padding: 2rem 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 3rem;
}

h1 {
  font-weight: 800;
  font-size: 2.8rem;
  text-align: center;
  margin-bottom: 1rem;
  background: linear-gradient(90deg, #b76ee0, #6a1b9a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.grid {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: 2rem;
}

a.card {
  background: #2c2343; /* remove blur for mobile performance */
  border-radius: 20px;
  padding: 1.5rem;
  box-shadow: 0 4px 16px rgba(107, 44, 134, 0.25);
  color: #d0b8ff;
  text-decoration: none;
  font-weight: 600;
  display: flex;
  flex-direction: column;
  gap: 0.6rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

a.card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 24px rgba(107, 44, 134, 0.4);
}

a.card:focus-visible {
  outline: 3px solid #b76ee0;
  outline-offset: 4px;
}

.card-icon {
  font-size: 2.5rem;
  margin-bottom: 0.4rem;
}

.card-title {
  font-size: 1.4rem;
  color: #bfa1ff;
}

.card-desc {
  font-weight: 400;
  color: #a688d1;
  flex-grow: 1;
}

@media (max-width: 720px) {
  .container {
    padding: 1.5rem 1rem;
    gap: 2rem;
  }

  h1 {
    font-size: 2.2rem;
    margin-bottom: 2rem;
  }

  .card-icon {
    font-size: 2.2rem;
  }

  .card-title {
    font-size: 1.2rem;
  }

  .card-desc {
    font-size: 0.95rem;
  }

  a.card {
    padding: 1.25rem;
  }
}


@media (max-width: 400px) {
  h1 {
    font-size: 1.8rem;
  }
}


.page-wrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

main.container {
  flex: 1;
}

</style>


<div class="page-wrapper">
  <main class="container" role="main" aria-label="Zupid Insights Home (Dark)">
    <h1>Zupid Insights</h1>

    <div class="grid">
      {#each insightSections as section}
        <a href={section.route} class="card" aria-label={`Go to ${section.title}`}>
          <div class="card-icon" aria-hidden="true">{section.icon}</div>
          <div class="card-title">{section.title}</div>
          <div class="card-desc">{section.description}</div>
        </a>
      {/each}
    </div>

    <CuriousBanner />
  </main>

  <Footer isMainPage={true} />
</div>
