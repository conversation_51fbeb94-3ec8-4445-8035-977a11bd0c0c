<script>
  import data from '$lib/data/data.json';

  const topMovies = data.topMovies || [];
  const displayedMovies = topMovies.slice(0, 5);
</script>

<style>
  :global(body) {
    margin: 0;
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #0b0715, #1c1333);
    color: #e6e6e6;
    background-attachment: fixed;
  }

  .container {
    max-width: 960px;
    margin: 5rem auto;
    padding: 3rem 3rem 4rem 3rem;
    background: 
      linear-gradient(135deg, rgba(212, 138, 230, 0.2), rgba(96, 45, 126, 0.15)),
      rgba(255, 255, 255, 0.05);
    border-radius: 32px;
    box-shadow:
      0 8px 24px rgba(212, 138, 230, 0.25),
      inset 0 0 50px rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(35px);
    -webkit-backdrop-filter: blur(35px);
    border: 1.5px solid rgba(212, 138, 230, 0.3);
    transition: box-shadow 0.3s ease;
  }
  .container:hover {
    box-shadow:
      0 12px 38px rgba(212, 138, 230, 0.4),
      inset 0 0 70px rgba(255, 255, 255, 0.07);
  }

  h1 {
    text-align: center;
    font-weight: 800;
    font-size: 2.8rem;
    margin-bottom: 1.3rem;
    background: linear-gradient(90deg, #c085ff, #a14ce5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    user-select: none;
    text-shadow: 0 0 6px #a14ce5cc;
  }

  .description {
    text-align: center;
    font-size: 1.15rem;
    color: #c6b8ffdd;
    margin-bottom: 3rem;
    max-width: 640px;
    margin-left: auto;
    margin-right: auto;
    user-select: none;
    text-shadow: 0 0 8px rgba(168, 126, 224, 0.6);
  }

  ul.movie-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 1.8rem;
    padding: 0;
    list-style: none;
  }

  li.movie-item {
    background: rgba(255, 255, 255, 0.06);
    border: 2px solid #d48ae6cc;
    border-radius: 18px;
    overflow: hidden;
    transition:
      transform 0.35s ease,
      box-shadow 0.35s ease,
      border-color 0.35s ease;
    cursor: pointer;
    box-shadow:
      0 6px 14px rgba(212, 138, 230, 0.15);
  }

  li.movie-item:hover,
  li.movie-item:focus-visible {
    transform: translateY(-7px) scale(1.06);
    box-shadow:
      0 12px 38px rgba(212, 138, 230, 0.6);
    border-color: #f0a1ffcc;
    outline: none;
  }

  .movie-poster {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-bottom: 2px solid #d48ae6cc;
    transition: border-color 0.35s ease;
  }

  li.movie-item:hover .movie-poster,
  li.movie-item:focus-visible .movie-poster {
    border-color: #f0a1ffcc;
  }

  .movie-details {
    text-align: center;
    padding: 0.8rem 0.6rem 1.3rem;
  }

  .movie-ranking {
    font-size: 1rem;
    color: #ffd54f;
    font-weight: 800;
    margin-bottom: 0.4rem;
    text-shadow: 0 0 8px #ffd54fcc;
    user-select: none;
  }

  .movie-title {
    font-size: 1.15rem;
    font-weight: 700;
    color: #d48ae6;
    margin-bottom: 0.5rem;
    line-height: 1.3;
    height: 2.8rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-shadow: 0 0 6px #d48ae6bb;
    user-select: text;
  }

  .movie-year {
    font-size: 0.9rem;
    color: #ccc;
    font-weight: 500;
    user-select: none;
  }

  @media (max-width: 480px) {
    .movie-poster {
      height: 170px;
    }

    h1 {
      font-size: 2.3rem;
    }
  }
</style>

<div class="container" role="main" aria-label="Top 5 Favorite Movies and Shows on Zupid">
  <h1>Top 5 Favorite Movies & Shows 🎬</h1>
  <p class="description">
    Based on what users on Zupid love the most
  </p>

  <ul class="movie-list">
    {#each displayedMovies as movie, index}
      <li
        class="movie-item"
        tabindex="0"
        aria-label={`${movie.title}, released ${movie.year}, liked by ${movie.user_percentage}% users`}
      >
        <img
          class="movie-poster"
          src={`https://cdnpublic.zippybits.in/r2_zupid/movies/${movie.image_path}`}
          alt={`${movie.title} Poster`}
          loading="lazy"
          decoding="async"
        />
        <div class="movie-details">
          <div class="movie-ranking">#{index + 1}</div>
          <div class="movie-title">{movie.title}</div>
          <!-- <div class="movie-year">{movie.user_percentage}% users</div> -->
        </div>
      </li>
    {/each}
  </ul>
</div>
