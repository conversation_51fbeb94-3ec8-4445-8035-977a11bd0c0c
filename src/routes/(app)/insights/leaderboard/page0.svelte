<script>
  // Dummy data for top users
  let topUsers = [
    { username: "<PERSON><PERSON><PERSON>", score: 47, college: "IIT Kanpur" },
    { username: "<PERSON><PERSON><PERSON>", score: 44, college: "DU SRCC" },
    { username: "<PERSON><PERSON><PERSON>", score: 42, college: "IIT Delhi" },
    { username: "<PERSON><PERSON><PERSON>", score: 39, college: "BITS Pilani" },
    { username: "<PERSON><PERSON>", score: 37, college: "NIT Trichy" }
  ];

  // Dummy data for top colleges
  let topColleges = [
    { college: "IIT Kanpur", avgScore: 41.2 },
    { college: "DU SRCC", avgScore: 39.8 },
    { college: "IIT Delhi", avgScore: 38.5 },
    { college: "BITS Pilani", avgScore: 36.9 },
    { college: "NIT Trichy", avgScore: 35.7 }
  ];
</script>

<svelte:head>
  <title>Zupid Movie Buff Leaderboard | Match by Vibe, Not Just Looks</title>
  <meta name="description" content="See the top movie buffs and colleges on Zupid! Discover how we calculate Movie Match Scores to connect users through shared movie and TV show tastes. Join now to get featured and find your vibe!">
  <meta name="keywords" content="Zupid, dating app India, movie leaderboard, college leaderboard, movie match score, dating app India, shared interests, Gen Z dating, personality based dating">
  <link rel="canonical" href="https://www.zupid.net/movie-leaderboard" />
</svelte:head>

<div class="leaderboard-wrapper">
  <!-- Top Movie Buffs -->
  <div class="board">
    <h2>🎬 Top Movie Buffs (All Colleges)</h2>
    <ul>
      {#each topUsers as user, index}
        <li>
          <span class="left">{index + 1}. {user.username} ({user.college})</span>
          <span class="right">{user.score} pts</span>
        </li>
      {/each}
    </ul>
  </div>

  <!-- Top Colleges -->
  <div class="board">
    <h2>🏆 Colleges with Best Movie Match</h2>
    <ul>
      {#each topColleges as college, index}
        <li>
          <span class="left">{index + 1}. {college.college}</span>
          <span class="right">{college.avgScore} avg pts</span>
        </li>
      {/each}
    </ul>
  </div>
</div>

<section class="details-section">
  <h3>📊 What Does the Score Mean?</h3>
  <p>
    Your <strong>Movie Match Score</strong> reflects how many other users on Zupid share your taste in movies and TV shows.
    The more overlap you have with others in your college (based on the movies/shows you selected during onboarding), the higher your score!
  </p>
  <p>
    We calculate this by comparing your selected movies with others around you. You earn points each time there's a vibe match. So basically:
    <em>More common picks = higher score 💜</em>
  </p>
  <p class="cta-note">Want your college on the board? <a href="mailto:<EMAIL>">Contact us to get featured</a>.</p>
</section>

<style>
  .leaderboard-wrapper {
    max-width: 900px;
    margin: 2rem auto 1rem;
    display: grid;
    gap: 2rem;
    grid-template-columns: 1fr 1fr;
  }

  .board {
    background: #111;
    border: 2px solid #D48AE6;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 6px 16px rgba(203, 108, 230, 0.3);
    color: #eee;
  }

  h2 {
    text-align: center;
    color: #D48AE6;
    margin-bottom: 1rem;
    font-size: 1.4rem;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  li {
    display: flex;
    justify-content: space-between;
    padding: 0.6rem 0;
    border-bottom: 1px solid #333;
    font-size: 1rem;
  }

  li:last-child {
    border-bottom: none;
  }

  .left {
    font-weight: 600;
    color: #ddd;
  }

  .right {
    color: #bbb;
  }

  .details-section {
    max-width: 800px;
    margin: 2rem auto 3rem;
    color: #ccc;
    padding: 1.5rem 2rem;
    background: #0a0a0a;
    border: 1px solid #333;
    border-radius: 10px;
    text-align: center;
    font-size: 1rem;
  }

  .details-section h3 {
    color: #D48AE6;
    margin-bottom: 1rem;
    font-weight: 700;
  }

  .details-section p {
    margin-bottom: 1rem;
    line-height: 1.6;
  }

  .cta-note a {
    color: #D48AE6;
    text-decoration: underline;
    font-weight: 600;
  }

  .cta-note a:hover {
    color: #ffaaff;
  }

  @media (max-width: 700px) {
    .leaderboard-wrapper {
      grid-template-columns: 1fr;
      margin: 2rem 1rem 1rem;
    }
  }
</style>