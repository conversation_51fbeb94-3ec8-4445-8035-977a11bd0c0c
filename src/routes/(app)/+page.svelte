<svelte:head>
  <title>Zupid - Match by <PERSON>ibe, Not Just Looks | India's Leading Dating App</title>
  <meta name="description" content="Zupid - Match by Vibe, Not Just Looks. Discover India's leading dating app for genuine connections, based on personality & shared interests. Experience secure, spam-free dating with verified profiles across India.">
  <meta
      name="keywords"
      content="Zupid Dating App, Indian dating app, dating app India, best dating app India, Indian singles, Match by Vibe, Not Just Looks, Vibe-based dating, Interest-based dating, AI-powered dating, Secure dating platform India, Personality matching, Deeper connections, Online dating India, Chatting app India, Compatibility matching, New dating app India, anti-scam dating India, verified profiles dating app">
  <link rel="canonical" href="https://www.zupid.net"/>
  <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
  <!-- <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet"> -->
</svelte:head>
<script>
  import Footer from '$lib/footer.svelte';
  import BottomBanner from '$lib/bottom-banner.svelte';
  let right = "-200px";
  let showMenuButton = true; 
      function showMenu(){
        right = "0px";
        showMenuButton = false; 
      }
      function hideMenu(){
        right = "-200px";
        showMenuButton = true;
      }

  

</script>

<div class="div-main-content">
  <section class="page1" id="id_page1">
    <nav>
      <!-- svelte-ignore a11y-missing-attribute -->
      <img src="/zupid_length_400.webp" alt="Zupid Logo">
        <div class="nav-links" id="navLinks" style="right:{right};">
          <i class="fa fa-times" on:click={hideMenu}></i>
          <ul>
            <li> <a href="/#about" onclick="amplitudeClickFunction('Click_AboutUs')" on:click={hideMenu}>About Us</a></li>
            <li> <a href="/campus-ambassador" onclick="amplitudeClickFunction('Click_campusAmbassador')" on:click={hideMenu}>Campus Ambassador</a></li>
            
            <li> <a href="/insights" onclick="amplitudeClickFunction('Click_Insights')"  on:click={hideMenu}>Insights</a></li>
            <li> <a href="/blog" onclick="amplitudeClickFunction('Click_Blog')" on:click={hideMenu}>Blog</a></li>
            
            
            <li> <a href="#contact-us" onclick="amplitudeClickFunction('Click_Contact')" on:click={hideMenu}>Contact</a></li>
          </ul>
        </div>
        {#if showMenuButton}
          <i on:click={showMenu} class="fa fa-bars"></i>
        {/if}
    </nav>
      <main>
        <div class="hero-content">
          <div class="download-app-div">
            <h1 class="tagline">Match by Vibe, <br><span class="highlight">Not Just Looks</span></h1>
            <p class="subtagline">Connect through what you love. Find someone who gets you.</p>
            <!-- <a href="https://play.google.com/store/apps/details?id=in.zippybits.zupid" target="_blank" onclick="amplitudeClickFunction('Click_Download')"><button class="download-button">Download App</button></a> -->
          </div>
          <div class="app-pic-div">
            <iframe 
              width="100%" 
              height="315" 
              src="https://www.youtube.com/embed/w8YmUtR1ubM" 
              title="Zupid Dating App Video" 
              frameborder="0" 
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
              allowfullscreen
              class="youtube-video"
            ></iframe>
          </div>
        </div>
      </main>
    </section>

    <!-- page2 -->
    <section class="page2" id="about">
      <div class="section-container">
          <h2 class="section-heading">About Us</h2>
          <div class="about-content">
            <p class="about-text">Zupid is a unique dating app that goes beyond just looks, helping singles connect based on shared interests and personality traits. Whether it's movies, music, books, sports, travel, or lifestyle preferences, we match people through what truly matters to them. By focusing on common passions and compatibility, Zupid fosters real connections and meaningful relationships.</p>

            <div class="features-grid">
              <div class="feature-item">
                <div class="feature-icon">🔍</div>
                <h3>3D Matching System</h3>
                <p>Matches based on <strong>Looks + Personality + Interests</strong> - the complete picture</p>
              </div>
              <div class="feature-item">
                <div class="feature-icon">🧠</div>
                <h3>Personality Profiling</h3>
                <p>Rated across <strong>5 key traits</strong> for better matches</p>
              </div>
              <div class="feature-item">
                <div class="feature-icon">🎯</div>
                <h3>Interest Matching</h3>
                <p><strong>20+ categories</strong> to find your shared passions</p>
              </div>
              <div class="feature-item">
                <div class="feature-icon">✅</div>
                <h3>Authentic Profiles Only</h3>
                <p>Manual verification • No shadowban • Anti-spam</p>
              </div>
            </div>
            
            <div class="cta-section">
              <p>Ready to meet someone who shares your vibe?</p>
              <h3>Join Zupid today.</h3>
            </div>
          </div>
      </div>
    </section>
    <!-- Campus Ambassador Section -->
<section class="page2" id="campus-ambassador">
  <div class="section-container">
    <h2 class="section-heading">Campus Ambassador</h2>
    <div class="about-content">
      <p class="about-text">
        Are you a college student who loves startups, networking, and community-building? Zupid's Campus Ambassador Program lets you represent India’s most vibe-driven dating app on your campus.
        <br><br>
        Enjoy rewards, certificates, mentorship, and insider access to product features - all while building something meaningful.
      </p>
      <div class="cta-section">
        <a href="https://www.zupid.net/campus-ambassador" target="_blank">
          <button class="download-button">Apply Now</button>
        </a>
      </div>
    </div>
  </div>
</section>

<!-- Insights Section -->
<section class="page2" id="insights">
  <div class="section-container">
    <h2 class="section-heading">Dating Insights</h2>
    <div class="about-content">
      <p class="about-text">
        From food preferences in Karnataka to music tastes in Kolkata - our Insights reveal how people across India vibe.
        <br><br>
        Get a peek into India's dating culture, trends, and interests - all powered by real Zupid user data.
      </p>
      <div class="cta-section">
        <a href="https://www.zupid.net/insights" target="_blank">
          <button class="download-button">Explore Now</button>
        </a>
      </div>
    </div>
  </div>
</section>


    <!-- page4 -->
    <section class="page4" id="contact-us">
      <div class="section-container">
          <h2 class="section-heading">Contact</h2>
          <div class="contact-content">
            <div class="contact-item">
              <h3>Email Address</h3>
              <a href="mailto:<EMAIL>"><EMAIL></a>
            </div>
            <!-- <div class="contact-item">
              <h3>Company</h3>
              <p>Zippybits Technologies Private Limited</p>
            </div> -->
          </div>
          <Footer isMainPage={true}/>
      </div>
    </section>
</div>
<BottomBanner />

<style>
  :global(body) {
    margin: 0;
    padding: 0;
    font-family: 'Poppins', sans-serif;
    scroll-behavior: smooth;
  }
  
  .div-main-content {
    overflow-x: hidden;
  }
  
  /* Page 1 - Hero Section */
  .page1 {
    padding-top: 20px;
    min-height: 100vh;
    width: 100%;
    background-color: #000000;
    position: relative;
  }
  
  nav {
    display: flex;
    padding: 2% 6%;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 10;
  }
  
  nav img {
    width: 180px;
    height: auto;
    transition: all 0.3s ease;
  }
  
  nav img:hover {
    transform: scale(1.05);
  }
  
  .nav-links {
    flex: 1;
    text-align: right;
    position: fixed;
    top: 0;
    right: -200px;
    height: 100vh;
    width: 200px;
    background-color: #000000;
    z-index: 20;
    transition: right 0.5s ease;
    padding-top: 60px;
  }
  
  .nav-links ul {
    padding: 0;
    margin: 0;
  }
  
  .nav-links ul li {
    list-style: none;
    padding: 15px 0;
    text-align: center;
  }
  
  .nav-links a {
    color: #cb6ce6;
    text-decoration: none;
    font-size: 18px;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
  }
  
  .nav-links a:hover {
    color: #ffffff;
  }
  
  .nav-links a::after {
    content: "";
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: #cb6ce6;
    transition: width 0.3s ease;
  }
  
  .nav-links a:hover::after {
    width: 100%;
  }
  
  nav .fa {
    color: #cb6ce6;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 30;
  }
  
  nav .fa:hover {
    transform: scale(1.1);
  }
  
  .fa-times {
    position: absolute;
    top: 20px;
    right: 20px;
  }
  
  .hero-content {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0 6%;
    height: calc(100vh - 100px);
  }
  
  .download-app-div {
    flex: 1;
    max-width: 600px;
    padding-right: 40px;
  }
  
  .tagline {
    font-size: 3.5rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 20px;
    line-height: 1.2;
  }
  
  .highlight {
    color: #cb6ce6;
  }
  
  .subtagline {
    font-size: 1.5rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 40px;
    font-weight: 300;
  }
  
  .download-button {
    display: inline-block;
    padding: 15px 40px;
    background-color: #cb6ce6;
    color: #000000;
    border: none;
    border-radius: 50px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(203, 108, 230, 0.3);
  }
  
  .download-button:hover {
    background-color: #db6de6;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(203, 108, 230, 0.4);
  }
  
  .app-pic-div {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .app-pic {
    width: 100%;
    max-width: 350px;
    border-radius: 30px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
    transition: all 0.5s ease;
  }
  
  .app-pic:hover {
    transform: translateY(-10px);
    box-shadow: 0 35px 60px rgba(0, 0, 0, 0.6);
  }
  
  /* Page 2 - About Section */
  .page2 {
    min-height: 100vh;
    width: 100%;
    background-color: #000000;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 80px 0;
  }
  
  .section-container {
    width: 100%;
    max-width: 1200px;
    padding: 0 40px;
  }
  
  .section-heading {
    font-size: 3rem;
    font-weight: 700;
    color: #cb6ce6;
    text-align: center;
    margin-bottom: 60px;
    position: relative;
  }
  
  .section-heading::after {
    content: "";
    display: block;
    width: 80px;
    height: 4px;
    background-color: #cb6ce6;
    margin: 20px auto 0;
  }
  
  .about-content {
    color: #ffffff;
  }
  
  .about-text {
    font-size: 1.2rem;
    line-height: 1.8;
    margin-bottom: 60px;
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }
  
  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin: 60px 0;
  }
  
  .feature-item {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    transition: all 0.3s ease;
  }
  
  .feature-item:hover {
    transform: translateY(-10px);
    background-color: rgba(203, 108, 230, 0.1);
  }
  
  .feature-icon {
    font-size: 2.5rem;
    margin-bottom: 20px;
  }
  
  .feature-item h3 {
    color: #cb6ce6;
    font-size: 1.5rem;
    margin-bottom: 15px;
  }
  
  .feature-item p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
    line-height: 1.6;
  }
  
  .cta-section {
    text-align: center;
    margin-top: 60px;
  }
  
  .cta-section p {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 10px;
  }
  
  .cta-section h3 {
    font-size: 2rem;
    color: #cb6ce6;
    margin-bottom: 20px;
  }
  
  .community-text {
    font-size: 1.5rem;
    color: #000000;
    font-weight: 600;
    margin-top: 30px;
  }
  
  /* Page 4 - Contact Section */
  .page4 {
    min-height: 100vh;
    width: 100%;
    background-color: #000000;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 80px 0;
  }
  
  .contact-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 50px;
    width: 100%;
    max-width: 1000px;
    margin: 60px auto;
  }
  
  .contact-item {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 40px;
    text-align: center;
    transition: all 0.3s ease;
  }
  
  .contact-item:hover {
    transform: translateY(-10px);
    background-color: rgba(203, 108, 230, 0.1);
  }
  
  .contact-item h3 {
    color: #cb6ce6;
    font-size: 1.5rem;
    margin-bottom: 20px;
  }
  
  .contact-item p, .contact-item a {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.1rem;
    line-height: 1.6;
    text-decoration: none;
    transition: color 0.3s ease;
  }
  
  .contact-item a:hover {
    color: #cb6ce6;
  }
  
  /* Responsive Styles */
  @media (max-width: 1024px) {
    .hero-content {
      flex-direction: column;
      justify-content: center;
      text-align: center;
      padding-top: 60px;
      height: auto;
    }
    
    .download-app-div {
      padding-right: 0;
      margin-bottom: 60px;
      max-width: 100%;
    }
    
    .tagline {
      font-size: 2.8rem;
    }
    
    .subtagline {
      font-size: 1.3rem;
    }
    
    .app-pic {
      max-width: 300px;
    }
  }
  
  @media (max-width: 768px) {
    .tagline {
      font-size: 2.2rem;
    }
    
    .subtagline {
      font-size: 1.1rem;
    }
    
    .section-heading {
      font-size: 2.2rem;
    }
    
    .about-text {
      font-size: 1rem;
    }
    
    .feature-item {
      padding: 20px;
    }
    
    .contact-item {
      padding: 30px;
    }
  }
  
  @media (max-width: 480px) {
    nav {
      padding: 20px 5%;
    }
    
    nav img {
      width: 140px;
    }
    
    .tagline {
      font-size: 1.8rem;
    }
    
    .section-container {
      padding: 0 20px;
    }
    
    .section-heading {
      font-size: 1.8rem;
    }
    
    .features-grid {
      grid-template-columns: 1fr;
    }
    
    .contact-content {
      grid-template-columns: 1fr;
    }
    .youtube-video {
  max-width: 350px;
  border-radius: 15px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
  transition: all 0.5s ease;
}

.youtube-video:hover {
  transform: translateY(-10px);
  box-shadow: 0 35px 60px rgba(0, 0, 0, 0.6);
}

@media (max-width: 768px) {
  .youtube-video {
    max-width: 300px;
  }
}
  }
</style>