<svelte:head>
    <title>Zupid - Resources</title>
    <meta name="description" content="Explore a wealth of dating resources tailored for the dating scene on Zupid. Discover expert advice, dating tips, relationship guides, and more to enhance your dating experience and find meaningful connections in India.">
    <meta
      name="keywords"
      content="Dating App, Modern dating app, Indian dating app,  Interest-based dating, Match through shared interests, Secure dating platform, Chatting app, Compatibility matching, AI-powered dating">
    <link rel="canonical" href="https://www.zupid.net/resources" />
    <style>
        /* Add this style to remove underline and change color */
        a {
            text-decoration: none; /* Remove underline */
            color: inherit; /* Inherit color from parent (removes blue color) */
        }
        .container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(min(500px, 100%), 1fr));
            gap: 20px;
            padding: 30px;
        }
        .post-title {
            margin-bottom: 5px;
            color: #cb6ce6;
            font-size: 15px;
        }
        .blog-title {
            text-align: center;
            font-size: 2rem;
            margin-top: 20px;
            grid-column: 1 / -1;
        }
    </style>
</svelte:head>

<script>
    import Footer from '$lib/footer.svelte';
    import { WEBSITE_ADDRESS } from "$lib/common-constant";

    export let data;
    let posts = data.posts;
</script>

<h1 class="blog-title" style="color: #db6de6; font-size: 30px;">Zupid Resources</h1>
<div class="blog-title" style="color: grey; font-size: 15px; padding-bottom: 10px;">Match by Vibe, Not Just Looks</div>
<div class="container">
    {#each posts as post, index}
        <a href="{WEBSITE_ADDRESS}/blog/{post.post_name}">
            <div class="post-title">{post.title}</div>
        </a>
    {/each}
</div>

<Footer isMainPage={true}/>
