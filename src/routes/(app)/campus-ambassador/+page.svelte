<svelte:head>
  <title>Zupid Campus Ambassador Program 🚀</title>
  <meta name="description" content="Become a Zupid Campus Ambassador – grow the dating revolution on your campus and earn exciting perks." />
  <meta name="keywords" content="campus ambassador, dating ambassador, college ambassador, Zupid ambassador, college marketing, genz campus" />
  <link rel="canonical" href="https://www.zupid.net/campus-ambassador" />
  <meta property="og:title" content="Zupid Campus Ambassador Program 🚀" />
  <meta property="og:description" content="Join <PERSON> as a Campus Ambassador and lead the dating revolution in your college with perks, leadership, and fun." />
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://www.zupid.net/campus-ambassador" />
  <meta property="og:image" content="https://www.zupid.net/heart_zupid_full_logo_500.png" />
</svelte:head>

<script>
  import Footer from '$lib/footer.svelte';
  
  const benefits = [
  { icon: '💎', title: '2 Years Premium', desc: 'Enjoy Zupid Premium free for two years — all features, no cost.' },
  { icon: '📜', title: 'Certificate + LOR', desc: 'Earn an official certificate and recommendation after 2 months.' },
  { icon: '🎓', title: 'Campus Influence', desc: 'Represent Zupid, host meme & dating events, and grow our community.' },
  { icon: '🏅', title: 'Exclusive Campus Ambassador Badge', desc: 'Show off your status with a special badge on your Zupid profile.' },
  { icon: '🚀', title: 'Startup Exposure', desc: 'Work with the core team and help shape how Gen Z connects.' },
  { icon: '💼', title: 'Priority for Internships & Jobs', desc: 'Get preference for paid internships and future hiring at Zupid.' }
];



</script>

<style>
  :global(body) {
    background: linear-gradient(135deg, #1e1a29, #2a2340);
    font-family: 'Poppins', sans-serif;
    color: #eee;
    margin: 0;
    padding: 0;
  }

  .container {
    max-width: 960px;
    margin: 0 auto;
    padding: 2.5rem 1.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 3rem;
  }

  h1 {
    font-size: 2.8rem;
    font-weight: 800;
    text-align: center;
    background: linear-gradient(90deg, #ff76cc, #cb6ce6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 1rem;
  }

  .subtitle {
    font-size: 1.15rem;
    color: #c7aadf;
    text-align: center;
    max-width: 720px;
  }

  .grid {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
  }

  .card {
    background: #2d2644;
    border-radius: 18px;
    padding: 1.5rem;
    box-shadow: 0 4px 14px rgba(203, 108, 230, 0.15);
    color: #dcd0f7;
    display: flex;
    flex-direction: column;
    gap: 0.6rem;
    transition: all 0.2s ease;
  }

  .card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(203, 108, 230, 0.3);
  }

  .card-icon {
    font-size: 2.5rem;
    margin-bottom: 0.3rem;
  }

  .card-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #e5caff;
  }

  .card-desc {
    font-size: 1rem;
    color: #a588d6;
  }

  .apply-button {
    display: inline-block;
    background: linear-gradient(to right, #ff76cc, #cb6ce6);
    color: #fff;
    font-weight: 600;
    border-radius: 999px;
    padding: 14px 36px;
    text-decoration: none;
    margin-top: 2rem;
    transition: background 0.3s ease;
  }

  .apply-button:hover {
    background: linear-gradient(to right, #ff85d6, #db6df2);
  }

  @media (max-width: 600px) {
    h1 { font-size: 2.2rem; }
    .card-icon { font-size: 2.2rem; }
    .card-title { font-size: 1.1rem; }
    .card-desc { font-size: 0.95rem; }
  }
</style>

<div class="container" role="main">
  <h1>Become a Zupid Campus Ambassador 🚀</h1>
  <p class="subtitle">
  Represent Zupid on campus. Get rewarded with premium access, a certificate, startup exposure & more - all for helping us grow.
  </p>


  <div class="grid">
    {#each benefits as item}
      <div class="card">
        <div class="card-icon">{item.icon}</div>
        <div class="card-title">{item.title}</div>
        <div class="card-desc">{item.desc}</div>
      </div>
    {/each}
  </div>

  <a class="apply-button" href="https://forms.gle/C7662AkuemsaefPp7" target="_blank" rel="noopener">
  Apply Now
</a>
</div>
<Footer isMainPage={true}/>
