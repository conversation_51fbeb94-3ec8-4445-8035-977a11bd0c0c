<script>
  import Footer from '$lib/footer.svelte';
  import Post from '$lib/post-item.svelte';
  import BottomBanner from '$lib/bottom-banner.svelte';

  export let data;
  let blog_images_folder = 'https://pub-d4e5dd63bd1b4795a9099259d8cb2e8a.r2.dev/zupid%2F';
  
  let post = data.post;
  let randomBlogPosts = data.randomBlogPosts;
</script>

<svelte:head>
  <title>{post.title}</title>
  <meta name="description" content="{post.title} - {post.body}">
  <link rel="canonical" href="https://www.zupid.net/blog/{post.post_name}" />
  <meta name="robots" content="index, follow">
</svelte:head>

<div class="wrapper">
  <div class="header-container">
    <a href="/" class="logo-link">
      <img 
        src="/zupid_blog_400.webp" 
        alt="Zupid Blog" 
        class="logo"
        on:error={(e) => {
          e.target.src = 'https://www.zupid.net/zupid_blog_400.webp';
          e.target.onerror = null;
        }}
      />
    </a>
  </div>
  
  <div class="post">
    <!-- Image Section -->
    <div class="image">
      <img 
        src="{blog_images_folder}{post.photo}" 
        alt="{post.title}" 
        class="post-image"
        on:error={(e) => {
          e.target.src = 'https://via.placeholder.com/800x400/121212/ffffff?text=Image+Not+Available';
          e.target.onerror = null;
        }}
      />
    </div>
    
    <!-- Content Section -->
    <div class="content">
      <h1 class="post-title">{post.title}</h1>
      <div class="post-body">{post.body}</div>
    </div>
    
    <div class="divider"></div>

    {#if randomBlogPosts && randomBlogPosts.length > 0}
    <div class="related-posts">
      <h2 class="related-title">Suggested Blogs</h2>
      <div class="posts-grid">
        {#each randomBlogPosts as blog}
        <Post post={blog} />
        {/each}
      </div>
    </div>
    {/if}
  </div>
</div>

<Footer isMainPage={true}/>
<BottomBanner />

<style>
  :global(body) {
    background-color: #0a0a0a;
    color: #e0e0e0;
    margin: 0;
    padding: 0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
  }

  .wrapper {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 2rem 1rem;
    background-color: #0a0a0a;
    min-height: 100vh;
    z-index: 1;
  }

  .header-container {
    position: relative;
    z-index: 100;
    width: 100%;
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
  }

  .logo-link {
    text-decoration: none;
    transition: transform 0.3s ease;
    display: inline-block;
  }

  .logo-link:hover {
    transform: scale(1.02);
  }

  .logo {
    height: 100px;
    filter: drop-shadow(0 2px 8px rgba(123, 97, 255, 0.3));
    position: relative;
  }
  
  /* Post Styles */
  .post {
    max-width: 800px;
    width: 100%;
    margin: 0 auto;
    padding: 2rem;
    background-color: #121212;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    border: 1px solid #252525;
    position: relative;
    z-index: 2;
  }

  /* Image Styles */
  .image {
    text-align: center;
    margin-bottom: 2rem;
  }

  .post-image {
    max-width: 100%;
    max-height: 400px;
    border-radius: 12px;
    object-fit: cover;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
    border: 1px solid #303030;
  }

  /* Content Styles */
  .content {
    padding: 0 1rem;
  }

  .post-title {
    color: #ffaa50;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.3;
  }

  .post-body {
    white-space: pre-line;
    color: #b0b0b0;
    font-size: 1.1rem;
    margin-bottom: 2rem;
  }

  .divider {
    height: 1px;
    background: linear-gradient(90deg, transparent, #404040, transparent);
    margin: 2.5rem 0;
  }

  /* Related Posts */
  .related-posts {
    margin-top: 2rem;
  }

  .related-title {
    color: #ffffff;
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    text-align: center;
  }

  .posts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(300px, 100%), 1fr));
    gap: 1.5rem;
  }

  @media (max-width: 768px) {
    .post {
      padding: 1.5rem;
      max-width: 95%;
    }

    .post-title {
      font-size: 1.7rem;
    }

    .post-body {
      font-size: 1rem;
    }
  }

  @media (max-width: 480px) {
    .logo {
      height: 80px;
    }

    .post {
      padding: 1rem;
    }

    .post-title {
      font-size: 1.5rem;
    }
  }
</style>