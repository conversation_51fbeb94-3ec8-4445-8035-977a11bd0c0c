<svelte:head>
    <title>Zupid Blog</title>
    <meta name="description" content="Explore dating tips, relationship advice, and success stories on the Zupid blog. Get insights into modern dating culture and learn how to build meaningful connections through shared interests. Join our community and discover better ways to date!" />
    <meta name="keywords" content="Zupid blog, dating tips, relationship advice, dating app blog, modern dating, connection building, dating success stories" />
    <link rel="canonical" href="https://www.zupid.net/blog" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
        :global(body) {
            margin: 0;
            padding: 0;
            font-family: 'Poppins', sans-serif;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        
        a {
            text-decoration: none;
            color: inherit;
        }
        
        .blog-header {
            background: linear-gradient(135deg, #000000 0%, #2a0a45 100%);
            padding: 3rem 1rem;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .blog-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            background: linear-gradient(90deg, #cb6ce6, #8a2be2);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        
        .blog-subtitle {
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 300;
        }
        
        .container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(min(350px, 100%), 1fr));
            gap: 2rem;
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 1rem;
            padding: 2rem;
            margin-top: 1rem;
        }
        
        .pagination-button {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            background-color: #cb6ce6;
            color: white;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .pagination-button:hover:not(:disabled) {
            background-color: #b45dd1;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(203, 108, 230, 0.3);
        }
        
        .pagination-button:disabled {
            background-color: #333333;
            color: #666666;
            cursor: not-allowed;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
                grid-template-columns: 1fr;
            }
            
            .blog-title {
                font-size: 2rem;
            }
        }
    </style>
</svelte:head>

<script>
    import Post from '$lib/post-item.svelte';
    import { getHomePagePosts } from '$lib/commonFunctions';
    import { POST_PER_OFFSET } from "$lib/common-constant";
    import { WEBSITE_ADDRESS } from "$lib/common-constant";
    import BottomBanner from '$lib/bottom-banner.svelte';
    import Footer from '$lib/footer.svelte';
    
    export let data;
    let posts = data.posts;
    let offset = 0 + POST_PER_OFFSET;
    let noMoreData = false;
    let currentPage = 0;
    let isLoading = false;

    let newPosts = [];

    async function fetchData() {
        if (isLoading) return;
        isLoading = true;
        
        try {
            newPosts = await getHomePagePosts(fetch, offset);
            if (newPosts != null && newPosts.length > 0) {
                posts = [...posts, ...newPosts];
                offset += POST_PER_OFFSET;
            } else {
                noMoreData = true;
            }
        } finally {
            isLoading = false;
        }
    }

    function previousPage() {
        if (currentPage > 0) {
            currentPage -= 1;
        }
    }

    async function nextPage() {
        await fetchData();
        if (!noMoreData || currentPage < Math.ceil(posts.length / POST_PER_OFFSET) - 1) {
            currentPage += 1;
        }
    }
</script>

<div class="blog-header">
    <a href="{WEBSITE_ADDRESS}/blog">
        <h1 class="blog-title">Zupid Blog</h1>
        <div class="blog-subtitle">Match by Vibe, Not Just Looks </div>
    </a>
</div>

<div class="container">
    {#each posts as post, index}
        {#if index >= (currentPage * POST_PER_OFFSET) && index < ((currentPage + 1) * POST_PER_OFFSET)}
        <Post post={post} />
        {/if}
    {/each}
</div>

<div class="pagination">
    <button 
        class="pagination-button" 
        on:click={previousPage}
        disabled={currentPage === 0 || isLoading}
    >
        Previous
    </button>
    <button 
        class="pagination-button" 
        on:click={nextPage}
        disabled={isLoading || (noMoreData && currentPage >= Math.floor(posts.length / POST_PER_OFFSET))}
    >
        Next
    </button>
</div>

<Footer isMainPage={true}/>
<BottomBanner />