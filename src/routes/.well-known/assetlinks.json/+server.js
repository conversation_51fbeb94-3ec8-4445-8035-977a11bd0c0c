/** @type {import('./$types').RequestHandler} */
export async function GET() {
  const body = sitemap();

  // Create the response
  const response = new Response(body);
  return response;
}

const sitemap = () => `[
    {
        "relation": ["delegate_permission/common.handle_all_urls"],
        "target": {
            "namespace": "android_app",
            "package_name": "com.example.myapp",
            "sha256_cert_fingerprints": [...]
        }
    }
]`;
